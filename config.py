import os
from dotenv import load_dotenv


load_dotenv()


class MRPConfig:
    """配置类，读取环境变量中的配置信息"""

    # API基础URL
    API_BASE = os.getenv('MRP_API_BASE')

    # 应用密钥
    APP_SECRET = os.getenv('MRP_APP_SECRET')

    # API路径
    MRP_PRODUCT_SEARCH = os.getenv('MRP_PRODUCT_SEARCH')
    API_PRODUCT_INFO = os.getenv('MRP_PRODUCT_INFO')
    API_PRODUCT_PRICE = os.getenv('MRP_PRODUCT_PRICE')
    API_PRODUCT_STOCK = os.getenv('MRP_PRODUCT_STOCK')
    API_UPLOAD_EXCEL_TYPE1 = os.getenv('MRP_UPLOAD_EXCEL_TYPE1')
    API_UPLOAD_EXCEL_TYPE2 = os.getenv('MRP_UPLOAD_EXCEL_TYPE2')
    API_TEMPLATE_LIST = os.getenv('MRP_TEMPLATE_LIST')

    @classmethod
    def get_full_url(cls, api_path):
        """获取完整的API URL

        Args:
            api_path: API路径

        Returns:
            完整的API URL
        """
        return f"{cls.API_BASE}{api_path}"

    @classmethod
    def get_search_url(cls):
        """获取模糊查询API的完整URL"""
        return cls.get_full_url(cls.MRP_PRODUCT_SEARCH)

    @classmethod
    def get_product_info_url(cls):
        """获取精确查询产品信息API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_INFO)

    @classmethod
    def get_product_price_url(cls):
        """获取精确查询产品价格API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_PRICE)

    @classmethod
    def get_product_stock_url(cls):
        """获取精确查询产品库存API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_STOCK)


    @classmethod
    def get_upload_excel_type1_url(cls):
        """获取上传Excel文件（类型1）的完整URL"""
        return cls.get_full_url(cls.API_UPLOAD_EXCEL_TYPE1)

    @classmethod
    def get_upload_excel_type2_url(cls):
        """获取上传Excel文件（类型2）的完整URL"""
        return cls.get_full_url(cls.API_UPLOAD_EXCEL_TYPE2)

    @classmethod
    def get_order_template_url(cls):
        """获取订单模板的完整URL"""
        return cls.get_full_url(cls.API_TEMPLATE_LIST)


class OpenaiConfig:
    # API 配置
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL")
    OPENAI_MODEL = os.environ.get("OPENAI_MODEL")
    MAX_TOKENS = int(os.environ.get("OPENAI_MAX_TOKENS", "40960"))
    TEMPERATURE = float(os.environ.get("OPENAI_TEMPERATURE", "0.7"))


class APIConfig:
    # 服务器配置
    HOST = os.environ.get("API_HOST", "0.0.0.0")
    PORT = int(os.environ.get("API_PORT", 8000))
    API_TOKEN = os.environ.get("API_TOKEN")


class LogConfig:
    # 日志配置
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    LOG_DIR = os.environ.get("LOG_DIR", "logs")
    LOG_RETENTION_DAYS = int(os.environ.get("LOG_RETENTION_DAYS", 30))
    LOG_COMPRESSION = os.environ.get("LOG_COMPRESSION", "zip")

    @classmethod
    def get_log_file_path(cls):
        """获取日志文件路径"""
        return os.path.join(cls.LOG_DIR, "app.log")


class FastGPTConfig:
    FASTGPT_DATASET_ID = os.environ.get("FASTGPT_DATASET_ID", "123456")
    FASTGPT_TOKEN = os.environ.get("FASTGPT_TOKEN", "sk-123456")
    FASTGPT_URL = os.environ.get("FASTGPT_URL", "http://127.0.0.1:31800/api/core/dataset/searchTest")