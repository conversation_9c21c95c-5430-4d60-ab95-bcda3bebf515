# 从url下载链接中下载 excle 文件，并获取表头内容
import requests
from pathlib import Path
from config import MRPConfig
from ai_mcp.query import get_order_template

def place_order(url: str, group_id: str, user_id: str, platform: str):
    """
    从给定的URL下载Excel文件并获取表头内容，根据表头内容就行下单。

    :param url: Excel文件的URL，例如：http://demo.rmsys.cn/dmt/Upload/chat/Sys_Company202503081312117791983ee790a74220/20250627/202506272131322162751.xls
    :return: 表头内容列表
    """
    response = requests.get(url)
    if response.status_code == 200:
        # 使用 url 中的文件名，作为临时文件名
        file_path = Path(f"{url.split('/')[-1]}").resolve()
        with open(file_path, 'wb') as file:
            file.write(response.content)

        # 使用 pandas 读取 Excel 文件
        import pandas as pd
        df = pd.read_excel(file_path)
        # 获取表头内容
        columns = df.columns.tolist()

        order_type = get_order_type(columns)
        if order_type == "type1":
            api_url = MRPConfig.get_upload_excel_type1_url()
        elif order_type == "type2":
            api_url = MRPConfig.get_upload_excel_type2_url()
        else:
            # 获取订单模板
            order_template = get_order_template(group_id, user_id, platform)
            if not order_template:
                return []



def get_table_data(url: str) -> list:
    """
    从给定的URL下载Excel文件并获取表头内容。

    :param url: Excel文件的URL，例如：http://demo.rmsys.cn/Upload/20250626.xls
    :return: 表头内容列表
    """
    response = requests.get(url)
    if response.status_code == 200:
        # 使用 url 中的文件名，作为临时文件名
        file_path = Path(f"{url.split('/')[-1]}").resolve()
        with open(file_path, 'wb') as file:
            file.write(response.content)

        # 使用 pandas 读取 Excel 文件
        import pandas as pd
        df = pd.read_excel(file_path)
        # 获取表头内容
        columns = df.columns.tolist()
        # 删除临时文件
        file_path.unlink(missing_ok=True)
        return columns
    else:
        return []


def get_order_type(columns: list) -> str:
    """
    根据表头内容判断订单类型。
    """
    order_type_1 = ["CMS客户名称", "CMS销售员姓名", "CMS商品编码"]
    order_type_2 = ["CMS客户编码", "CMS销售员编码", "订单平台"]

    if all(col in columns for col in order_type_1):
        return "type1"
    elif all(col in columns for col in order_type_2):
        return "type2"
    else:
        return "unknown"
